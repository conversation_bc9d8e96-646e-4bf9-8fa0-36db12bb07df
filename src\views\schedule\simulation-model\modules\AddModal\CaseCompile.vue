<template>
  <div class="case-compile-container">
    <!-- <div class="page-title">仿真方案编制</div> -->

    <div class="main-content">
      <!-- 左侧区域 -->
      <div class="left-section">
        <!-- 地图区域 -->
        <div class="map-section">
          <div class="map-placeholder">
            <!-- 暂时显示占位文本，后续实现地图功能 -->
            <div class="placeholder-text">地图区域</div>
          </div>
        </div>

        <!-- 折线图区域 -->
        <div class="chart-section">
          <div class="section-title">东干渠</div>
          <div class="chart-container">
            <div ref="eastCanalChart" class="east-canal-chart" style="height: 100%; width: 100%;"></div>
          </div>
        </div>
      </div>

      <!-- 右侧区域 -->
      <div class="right-section">
        <!-- 供需水表格 -->
        <div class="supply-demand-section">
          <div class="section-header">
            <div class="header-left">
              <div class="section-title">供需水</div>
             <div class="supply-input">
                <label>实际可供水量：</label>
                <a-input-number
                  v-model="supplyDemandData.actualSupply"
                  :min="0"
                  :step="0.01"
                  placeholder="请输入"
                  style="width: 150px; margin-left: 8px;"
                />
                <span style="margin-left: 4px;">万m³</span>
              </div>
            </div>
            <div class="header-right">

              <a-button type="primary" @click="handleSupplyDemandBatchImport" class="batch-import-btn">
                批量导入
              </a-button>
            </div>
          </div>
          <div class="table-container">
            <VxeTable
              ref="supplyDemandTableRef"
              size="small"
              :columns="supplyDemandColumns"
              :tableData="supplyDemandData.tableData"
              :tablePage="false"
              :isShowTableHeader="false"
            />
          </div>
        </div>

        <!-- 下方两个表格 -->
        <div class="bottom-tables">
          <!-- 水位表格 -->
          <div class="water-level-section">
            <div class="section-header">
              <div class="section-title">水位</div>
              <a-button type="primary" @click="handleWaterLevelBatchImport" class="batch-import-btn">
                批量导入
              </a-button>
            </div>
            <div class="table-container">
              <VxeTable
                ref="waterLevelTableRef"
                size="small"
                :columns="waterLevelColumns"
                :tableData="waterLevelData"
                :tablePage="false"
                :isShowTableHeader="false"
              />
            </div>
          </div>

          <!-- 流量表格 -->
          <div class="flow-section">
            <div class="section-header">
              <div class="section-title">流量</div>
              <a-button type="primary" @click="handleFlowBatchImport" class="batch-import-btn">
                批量导入
              </a-button>
            </div>
            <div class="table-container">
              <VxeTable
                ref="flowTableRef"
                size="small"
                :columns="flowColumns"
                :tableData="flowData"
                :tablePage="false"
                :isShowTableHeader="false"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 批量导入弹窗 -->
    <SimulationBatchImportModal
      :visible.sync="batchImportModal.visible"
      :nameList="batchImportModal.nameList"
      :sourceType="batchImportModal.sourceType"
      @save="handleBatchImportSave"
    />
  </div>
</template>

<script lang="jsx">
  import moment from 'moment'
  import VxeTable from '@/components/VxeTable/index.vue'
  import * as echarts from 'echarts'
  import { getChDeptFlow, forecast, getInWaterPage, getChWaterList, getMaxOutWater } from '../../services'
  import SimulationBatchImportModal from './SimulationBatchImportModal.vue'

  export default {
    name: 'CaseCompile',
    props: ['baseInfo', 'projectFlows'],
    components: {
      VxeTable,
      SimulationBatchImportModal
    },
    data() {
      return {
        // 原有数据
        mapIns: null,
        dispatchCaseOptions: {},
        tableKey: 1,
        loading: false,
        allData: [],
        active: undefined,
        tableData: [],
        tableColumns: [],

        // 悬浮状态
        hoveredRowIndex: -1,
        hoveredField: '',
        hoveredTableType: '', // 'supplyDemand', 'waterLevel', 'flow'

        // 批量导入弹窗
        batchImportModal: {
          visible: false,
          nameList: [],
          sourceType: '' // 'demandWater', 'waterLevel', 'flow'
        },

        // 供需水数据
        supplyDemandData: {
          actualSupply: undefined, // 实际可供水量
          tableData: [] // 需水口数据
        },

        // 水位数据
        waterLevelData: [],

        // 流量数据
        flowData: [],

        // 折线图数据（保留原结构，后续不再使用 LineEchart）
        chartData: {
          dataSource: [
            {
              name: '水位',
              color: '#1890ff', // 蓝色
              data: [] // 格式: [['渠道断面', 水位值], ['渠道断面', 水位值], ...]
            }
          ],
          custom: {
            shortValue: true,
            dataZoom: false,
            showAreaStyle: false,
            xLabel: '渠道断面',
            yLabel: '水位(m)',
            legend: false,
            grid: {
              left: '10%',
              right: '10%',
              top: '15%',
              bottom: '15%',
              containLabel: true
            }
          }
        },

        // 直接渲染东干渠折线图
        eastCanalChart: null
      }
    },
    computed: {
      // 供需水表格列配置
      supplyDemandColumns() {
        return [
          {
            field: 'waterIntakeName',
            title: '需水口',
            width: '50%',
            align: 'left'
          },
          {
            field: 'demandWater',
            title: '需水量(万m³)',
            width: '50%',
            align: 'left',
            slots: {
              default: ({ row, rowIndex }) => {
                return this.renderEditableCell(row, rowIndex, 'demandWater', 'supplyDemand')
              }
            }
          }
        ]
      },

      // 水位表格列配置
      waterLevelColumns() {
        return [
          {
            field: 'dispatchObject',
            title: '调度对象',
            width: '50%',
            align: 'left'
          },
          {
            field: 'waterLevel',
            title: '水位(m)',
            width: '50%',
            align: 'left',
            slots: {
              default: ({ row, rowIndex }) => {
                return this.renderEditableCell(row, rowIndex, 'waterLevel', 'waterLevel')
              }
            }
          }
        ]
      },

      // 流量表格列配置
      flowColumns() {
        return [
          {
            field: 'dispatchObject',
            title: '调度对象',
            width: '50%',
            align: 'left'
          },
          {
            field: 'flow',
            title: '流量(m³/s)',
            width: '50%',
            align: 'left',
            slots: {
              default: ({ row, rowIndex }) => {
                return this.renderEditableCell(row, rowIndex, 'flow', 'flow')
              }
            }
          }
        ]
      }
    },
    watch: {
      baseInfo: {
        handler(newVal) {
          if (newVal && newVal.startTime && newVal.endTime && newVal.simulateType) {
            this.loadSupplyDemandData()
            this.loadWaterFlowData()
          }
        },
        immediate: true,
        deep: true
      }
    },
    created() {
      getInWaterPage({ pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
        //调度方案选项
        this.dispatchCaseOptions = (res.data?.data || []).map(el => ({
          ...el,
          label: el.caseName,
          value: el.inWaterId,
        }))
        console.log('this.dispatchCaseOptions 131', this.dispatchCaseOptions)
      })
    },
    mounted() {
      // 初始化 ECharts 实例
      this.$nextTick(() => {
        this.eastCanalChart = echarts.init(this.$refs.eastCanalChart)
      })
    },
    beforeDestroy() {
      if (this.eastCanalChart) {
        this.eastCanalChart.dispose()
        this.eastCanalChart = null
      }
    },
    methods: {
      // 渲染可编辑单元格
      renderEditableCell(row, rowIndex, field, tableType) {
        const h = this.$createElement

        return h('div', {
          class: 'editable-cell',
          style: {
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            width: '100%',
            padding: '4px'
          },
          on: {
            mouseenter: () => this.handleCellMouseEnter(rowIndex, field, tableType),
            mouseleave: () => this.handleCellMouseLeave()
          }
        }, [
          h('a-input-number', {
            props: {
              size: 'small',
              step: 0.01,
              min: 0,
              precision: 2,
              value: row[field]
            },
            style: {
              width: '100px'
            },
            on: {
              change: (value) => this.handleInputChange(rowIndex, field, value, tableType)
            }
          }),
          h('a', {
            style: {
              color: '#165DFF',
              textDecoration: 'none',
              fontSize: '12px',
              whiteSpace: 'nowrap',
              opacity: this.hoveredRowIndex === rowIndex && this.hoveredField === field && this.hoveredTableType === tableType ? 1 : 0,
              transition: 'opacity 0.2s ease',
              cursor: 'pointer'
            },
            on: {
              click: () => this.handleFillDown(rowIndex, field, tableType)
            }
          }, '向下填充')
        ])
      },

      // 处理单元格鼠标进入
      handleCellMouseEnter(rowIndex, field, tableType) {
        this.hoveredRowIndex = rowIndex
        this.hoveredField = field
        this.hoveredTableType = tableType
      },

      // 处理单元格鼠标离开
      handleCellMouseLeave() {
        this.hoveredRowIndex = -1
        this.hoveredField = ''
        this.hoveredTableType = ''
      },

      // 处理输入框数值变化
      handleInputChange(rowIndex, field, value, tableType) {
        let targetData
        if (tableType === 'supplyDemand') {
          targetData = this.supplyDemandData.tableData
        } else if (tableType === 'waterLevel') {
          targetData = this.waterLevelData
        } else if (tableType === 'flow') {
          targetData = this.flowData
        }

        if (targetData && targetData[rowIndex]) {
          const newData = [...targetData]
          newData[rowIndex] = {
            ...newData[rowIndex],
            [field]: value || 0
          }

          if (tableType === 'supplyDemand') {
            this.supplyDemandData.tableData = newData
          } else if (tableType === 'waterLevel') {
            this.waterLevelData = newData
          } else if (tableType === 'flow') {
            this.flowData = newData
          }
        }
      },

      // 处理向下填充
      handleFillDown(fromIndex, field, tableType) {
        let targetData, fieldName
        if (tableType === 'supplyDemand') {
          targetData = this.supplyDemandData.tableData
          fieldName = '需水量'
        } else if (tableType === 'waterLevel') {
          targetData = this.waterLevelData
          fieldName = '水位'
        } else if (tableType === 'flow') {
          targetData = this.flowData
          fieldName = '流量'
        }

        if (!targetData || !targetData[fromIndex]) return

        const fillValue = targetData[fromIndex][field]
        const newData = [...targetData]

        // 向下填充到所有后续行
        for (let i = fromIndex + 1; i < newData.length; i++) {
          newData[i] = {
            ...newData[i],
            [field]: fillValue
          }
        }

        if (tableType === 'supplyDemand') {
          this.supplyDemandData.tableData = newData
        } else if (tableType === 'waterLevel') {
          this.waterLevelData = newData
        } else if (tableType === 'flow') {
          this.flowData = newData
        }

        // 隐藏悬浮状态
        this.hoveredRowIndex = -1
        this.hoveredField = ''
        this.hoveredTableType = ''

        this.$message.success(`已将${fieldName}向下填充`)
      },

      // 加载供需水数据
      async loadSupplyDemandData() {
        if (!this.baseInfo || !this.baseInfo.startTime || !this.baseInfo.endTime) {
          return
        }

        try {
          const params = {
            startTime: this.baseInfo.startTime,
            endTime: this.baseInfo.endTime,
            scene: this.baseInfo.simulateType
          }

          const response = await getChDeptFlow(params)
          console.log('供需水数据响应:', response)

          if (response.data) {
            const { records, supplyWaterValue } = response.data

            // 设置实际可供水量
            this.supplyDemandData.actualSupply = supplyWaterValue

            // 设置需水口数据
            this.supplyDemandData.tableData = (records || []).map(item => ({
              waterIntakeName: item.deptName, // 需水口名称
              demandWater: item.waterDemandValue // 需水量(万m³)
            }))
          }
        } catch (error) {
          console.error('获取供需水数据失败:', error)
          this.$message.error('获取供需水数据失败')
        }
      },

      // 加载水位流量数据
      async loadWaterFlowData() {
        try {
          const response = await getChWaterList()
          console.log('水位流量数据响应:', response)

          if (response.data ) {
            const riverData = response.data || []

            // 处理水位和流量数据
            let waterLevelData = []
            let flowData = []
            let chartData = []

            // 遍历所有河道数据
            riverData.forEach(river => {
              if (river.sites && river.sites.length > 0) {
                river.sites.forEach(site => {
                  // 水位数据
                  waterLevelData.push({
                    dispatchObject: site.siteName,
                    waterLevel: site.wlv
                  })

                  // 流量数据
                  flowData.push({
                    dispatchObject: site.siteName,
                    flow: site.flow
                  })
                })
              }
            })

            // 设置表格数据
            this.waterLevelData = waterLevelData
            this.flowData = flowData

            // 更新折线图数据 - 使用第二个河道的数据（data[1]）
            if (riverData.length > 1 && Array.isArray(riverData[1].sites) && riverData[1].sites.length > 0) {
              chartData = riverData[1].sites
                .filter(site => site.mileage !== undefined && site.mileage !== null && site.wlv !== undefined && site.wlv !== null)
                .sort((a, b) => Number(a.mileage) - Number(b.mileage))
                .map(site => [
                  Number(site.mileage), // 横坐标：渠道断面（里程）
                  Number(site.wlv)      // 纵坐标：水位
                ])

              this.chartData = {
                dataSource: [
                  {
                    name: '水位',
                    color: '#1890ff',
                    data: chartData
                  }
                ],
                custom: {
                  shortValue: true,
                  dataZoom: false,
                  showAreaStyle: false,
                  xLabel: '渠道断面',
                  yLabel: '水位(m)',
                  legend: false,
                  grid: {
                    left: '10%',
                    right: '10%',
                    top: '15%',
                    bottom: '15%',
                    containLabel: true
                  }
                }
              }

              // 渲染东干渠折线图
              this.updateEastCanalChart(chartData)

            }
          }
        } catch (error) {
          console.error('获取水位流量数据失败:', error)

          this.$message.error('获取水位流量数据失败')
        }
      },

      onMapMounted(mapIns) {
        this.mapIns = mapIns
        this.$nextTick(() => {
          this.mapIns.resize()
        })
        this.mapOverlayIns = new MapboxOverlay({
          id: 'deck-geojson-layer-overlay',
          layers: [],
        })
        this.mapIns.addControl(this.mapOverlayIns)
        this.dealLayers()
      },
      onTabChange(activeKey) {
        this.tableData = this.allData.find(el => el.projectId === activeKey).res
        this.tableColumns = this.allData.find(el => el.projectId === activeKey).columns
        this.tableKey += 1
      },
      // 供需水批量导入
      handleSupplyDemandBatchImport() {
        this.batchImportModal = {
          visible: true,
          nameList: this.supplyDemandData.tableData.map(item => item.waterIntakeName),
          sourceType: 'demandWater'
        }
      },

      // 水位批量导入
      handleWaterLevelBatchImport() {
        this.batchImportModal = {
          visible: true,
          nameList: this.waterLevelData.map(item => item.dispatchObject),
          sourceType: 'waterLevel'
        }
      },

      // 流量批量导入
      handleFlowBatchImport() {
        this.batchImportModal = {
          visible: true,
          nameList: this.flowData.map(item => item.dispatchObject),
          sourceType: 'flow'
        }
      },

      // 批量导入保存
      handleBatchImportSave(importData) {
        if (this.batchImportModal.sourceType === 'demandWater') {
          // 处理供需水数据
          const newData = this.supplyDemandData.tableData.map((item, idx) => {
            const importItem = importData[idx]
            if (!importItem) return { ...item }
            return {
              ...item,
              demandWater: importItem.demandWater || 0
            }
          })
          this.supplyDemandData.tableData = newData
        } else if (this.batchImportModal.sourceType === 'waterLevel') {
          // 处理水位数据
          const newData = this.waterLevelData.map((item, idx) => {
            const importItem = importData[idx]
            if (!importItem) return { ...item }
            return {
              ...item,
              waterLevel: importItem.waterLevel || 0
            }
          })
          this.waterLevelData = newData
        } else if (this.batchImportModal.sourceType === 'flow') {
          // 处理流量数据
          const newData = this.flowData.map((item, idx) => {
            const importItem = importData[idx]
            if (!importItem) return { ...item }
            return {
              ...item,
              flow: importItem.flow || 0
            }
          })
          this.flowData = newData
        }

        this.batchImportModal.visible = false
        this.$message.success('批量导入成功')
      },

      save() {
        // 直接调用保存数据方法，不需要表单验证
        this.saveFormData()
      },
      // 保存数据并调用仿真接口
      async saveFormData() {
        try {
          // 调度方案 默认第一个
          if (this.dispatchCaseOptions && this.dispatchCaseOptions.length > 0) {
            const inWaterId = this.dispatchCaseOptions[0].inWaterId
            const maxOutWaterRes = await getMaxOutWater({ inWaterId: inWaterId })

            // 获取当前的供需水和水位流量数据
            const params = {
              startTime: this.baseInfo.startTime,
              endTime: this.baseInfo.endTime,
              scene: this.baseInfo.simulateType,
            }

            const chDeptFlowRes = await getChDeptFlow(params)
            const waterFlowRes = await getChWaterList()

            // 重新拼装供需水数据
            const chDeptFlows = {
              records: this.supplyDemandData.tableData.map(item => {
                // 从原始数据中查找对应的 deptCode
                const originalItem = chDeptFlowRes.data.records?.find(record => record.deptName === item.waterIntakeName)
                return {
                  deptCode: originalItem?.deptCode || '',
                  deptName: item.waterIntakeName,
                  waterDemandValue: item.demandWater || 0
                }
              }),
              supplyWaterValue: this.supplyDemandData.actualSupply || 0
            }

            // 重新拼装水位流量数据
            const chSiteWaters = waterFlowRes.data.map(river => ({
              ...river,
              sites: river.sites.map(site => {
                // 从表格数据中获取更新后的水位和流量值
                const waterLevelItem = this.waterLevelData.find(item => item.dispatchObject === site.siteName)
                const flowItem = this.flowData.find(item => item.dispatchObject === site.siteName)

                return {
                  ...site,
                  wlv: waterLevelItem ? waterLevelItem.waterLevel : site.wlv,
                  flow: flowItem ? flowItem.flow : site.flow
                }
              })
            }))

            // 构建请求参数
            const forecastParams = {
              caseName: this.baseInfo.caseName || '',
              chDeptFlows: chDeptFlows,
              chSiteWaters: chSiteWaters,
              outWaters: maxOutWaterRes.data,
              endTime: this.baseInfo.endTime,
              inWaterId: inWaterId,
              scene: this.baseInfo.simulateType,
              startTime: this.baseInfo.startTime
            }

            console.log('调用仿真接口参数:', forecastParams)

            // 调用仿真接口
            const forecastRes = await forecast(forecastParams)
            console.log('仿真接口响应:', forecastRes)

            if (forecastRes.data) {
              // 返回渠道水动力id
              this.$emit('saveData', forecastRes.data)
            } else {
              this.$message.error('仿真方案生成失败')
              this.$emit('saveData', false)
            }
          }
        } catch (error) {
          console.error('获取数据失败:', error)
          this.$message.error('获取数据失败，请重试')
          this.$emit('saveData', false)
        }
      },

      // 初始化与更新东干渠折线图
      initEastCanalChart() {
        if (!this.eastCanalChart && this.$refs.eastCanalChart) {
          this.eastCanalChart = echarts.init(this.$refs.eastCanalChart)
        }
      },
      updateEastCanalChart(points = []) {
        // points: [[mileage, wlv], ...]
        this.initEastCanalChart()
        if (!this.eastCanalChart) return
        const option = {
          tooltip: { trigger: 'axis' },
          grid: { left: '10%', right: '10%', top: '15%', bottom: '15%', containLabel: true },
          xAxis: {
            type: 'category',
            name: '渠道断面',
            boundaryGap: false,
            data: points.map(p => p[0])
          },
          yAxis: {
            type: 'value',
            name: '水位(m)'
          },
          series: [
            {
              name: '水位',
              type: 'line',
              smooth: true,
              showSymbol: true,
              symbolSize: 6,
              lineStyle: { color: '#1890ff' },
              itemStyle: { color: '#1890ff' },
              areaStyle: undefined,
              data: points.map(p => p[1])
            }
          ]
        }
        this.eastCanalChart.setOption(option, true)
        this.$nextTick(() => {
          this.eastCanalChart && this.eastCanalChart.resize()
        })
      },

    }
           
  }
</script>

<style lang="less" scoped>
  .case-compile-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 16px;

    .page-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 16px;
    }

    .main-content {
      flex: 1;
      display: flex;
      gap: 20px;
      height: calc(100% - 50px);
    }

    .left-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .right-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      // margin-bottom: 12px;
      color: #333;
    }

    .map-section {
      flex: 2; // 地图区域高度是折线图的2倍
      padding: 16px;
      background: #fafafa;

      .map-placeholder {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f0f0f0;
        border-radius: 4px;

        .placeholder-text {
          color: #999;
          font-size: 16px;
        }
      }
    }

    .chart-section {
      flex: 1; // 折线图高度
      padding: 16px;

      .chart-container {
        height: 100%;
        padding: 10px; // 给图表添加内边距，避免坐标轴被遮挡
      }
    }

    .supply-demand-section {
      flex: 1;
      // padding: 16px;

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        .header-left {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .header-right {
          display: flex;
          align-items: center;
          gap: 12px;
        }
      }

      .supply-input {
        display: flex;
        align-items: center;

        label {
          font-weight: 500;
          color: #333;
        }
      }

      .table-container {
        height: calc(100% - 60px);
      }
    }

    .bottom-tables {
      flex: 1;
      display: flex;
      gap: 8px;
    }

    .water-level-section,
    .flow-section {
      flex: 1;

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
      }

      .table-container {
        height: calc(100% - 60px);
      }
    }
  }

  ::v-deep .vxe-table--render-default.size--small .vxe-header--row .vxe-header--column .vxe-cell {
    height: 40px !important;
  }

  ::v-deep .ant-radio-group {
    .ant-radio-wrapper {
      font-size: inherit;
      span {
        &:last-child {
          padding: 0 0 0 5px;
        }
      }
    }
  }

  ::v-deep .ant-empty-image svg {
    width: 100%;
  }

  // 可编辑单元格样式
  .editable-cell {
    transition: all 0.2s ease;
    border-radius: 4px;

    &:hover {
      background-color: rgba(22, 93, 255, 0.05);
    }

    a {
      transition: all 0.2s ease;

      &:hover {
        color: #3273FF !important;
        text-decoration: underline !important;
      }
    }
  }

  // 批量导入按钮样式
  .batch-import-btn {
    border-color: #165DFF;
    color: #fff !important;
    font-size: 12px;
    height: 28px;
    padding: 0 12px;

    &:hover {
      border-color: #3273FF;
      background-color: #3273FF;
    }
  }
</style>
