<template>
  <div style="flex: 1">
    <!-- 加载进度显示 -->
    <div
      v-if="showProgress"
      class="progress-container"
      style="height: 100%; width: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center; padding: 40px;"
    >
      <div style="text-align: center; margin-bottom: 30px;">
        <div class="step-name">{{ processInfo.stepname || '正在处理...' }}</div>
        <div class="progress-wrapper">
          <a-progress
            :percent="processInfo.process || 0"
            :show-info="true"
            stroke-color="#1890ff"
            :format="percent => `${percent}%`"
          />
        </div>
        <div v-if="processInfo.remaintime > 0" class="remain-time">
          预计剩余时间：{{ processInfo.remaintime }} 秒
        </div>
      </div>
    </div>

    <!-- 完成状态显示 -->
    <div
      v-else-if="showCompleted"
      class="completed-container"
      style="height: 100%; width: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center; padding: 40px;"
    >
      <div style="text-align: center;">
        <a-icon type="check-circle" class="success-icon" />
        <div class="success-text">方案已生成</div>
        <div class="view-result-btn">
          <a-button type="primary" @click="handleViewResult">
            点击查看<span style="color: #165DFF; font-weight: bold;">方案结果</span>
          </a-button>
        </div>
      </div>
    </div>

    <!-- 错误状态显示 -->
    <div
      v-else-if="!!errorInfo"
      style="height: 100%; width: 100%; display: flex; justify-content: center; align-items: center"
    >
      <a-result status="error" :sub-title="errorInfo"></a-result>
    </div>

    <!-- 结果面板 -->
    <div v-else-if="!!chSimId && showResultPanel" style="height: 100%">
      <ResultPanel :chSimId="chSimId" />
    </div>
  </div>
</template>

<script lang="jsx">
  import { getModelRunProcess } from '../../services'
  import { SocketClient } from '@/utils/sockClient.js'
  import ResultPanel from '../ResultPanel/index.vue'

  export default {
    name: 'Result',
    props: ['baseInfo', 'projectFlows', 'chSimId'],
    components: {
      ResultPanel,
    },
    data() {
      return {
        loading: false,
        socketIns: null,
        errorInfo: null,
        processInfo: {
          isover: 0,
          mid: '',
          moditime: '',
          msg: '',
          process: 0,
          remaintime: 0,
          stepname: ''
        },
        pollTimer: null,
        showResultPanel: false
      }
    },
    computed: {
      showProgress() {
        return this.chSimId && this.processInfo.isover === 0
      },
      showCompleted() {
        return this.chSimId && this.processInfo.isover === 1 && this.processInfo.process === 100
      }
    },
    watch: {
      chSimId: {
        handler(newVal) {
          if (newVal) {
            this.startPolling()
          }
        },
        immediate: true
      }
    },
    created() {
      this.$emit('update:isDisabledBtn', true)
      this.errorInfo = null
    },
    mounted() {},
    beforeDestroy() {
      this.stopPolling()
      if (this.socketIns) {
        this.socketIns.disconnect()
      }
    },
    methods: {
      // 开始轮询获取进度
      startPolling() {
        if (!this.chSimId) return

        this.getProgress()
        this.pollTimer = setInterval(() => {
          this.getProgress()
        }, 2000) // 每2秒轮询一次
      },

      // 停止轮询
      stopPolling() {
        if (this.pollTimer) {
          clearInterval(this.pollTimer)
          this.pollTimer = null
        }
      },

      // 获取模型运行进度
      async getProgress() {
        try {
          const response = await getModelRunProcess({ chSimId: this.chSimId })
          console.log('模型进度响应:', response)

          if (response.data) {
            this.processInfo = response.data

            // 如果完成了，停止轮询并启用按钮
            if (response.data.isover === 1 && response.data.process === 100) {
              this.stopPolling()
              this.$emit('update:isDisabledBtn', false)
            }

            // 如果有错误信息，显示错误
            if (response.data.msg) {
              this.errorInfo = response.data.msg
              this.stopPolling()
            }
          }
        } catch (error) {
          console.error('获取模型进度失败:', error)
          this.errorInfo = '获取模型进度失败，请重试'
          this.stopPolling()
        }
      },

      // 查看方案结果
      handleViewResult() {
        this.showResultPanel = true
      },

      save() {
        this.$emit('saveData', this.chSimId)
      },
    },
  }
</script>

<style lang="less" scoped>
.progress-container {
  .step-name {
    font-size: 18px;
    color: #333;
    margin-bottom: 20px;
    font-weight: 500;
  }

  .progress-wrapper {
    width: 400px;
    margin: 0 auto;

    ::v-deep .ant-progress-text {
      color: #1890ff;
      font-weight: 500;
    }
  }

  .remain-time {
    margin-top: 10px;
    color: #666;
    font-size: 14px;
  }
}

.completed-container {
  .success-icon {
    font-size: 48px;
    color: #52c41a;
    margin-bottom: 20px;
  }

  .success-text {
    font-size: 18px;
    color: #333;
    margin-bottom: 20px;
    font-weight: 500;
  }

  .view-result-btn {
    ::v-deep .ant-btn {
      height: 40px;
      padding: 0 24px;
      font-size: 16px;
    }
  }
}
</style>
