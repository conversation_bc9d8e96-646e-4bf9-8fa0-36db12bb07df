<template>
  <a-form-model
    style="margin-top: 24px"
    ref="form"
    :model="form"
    :rules="rules"
    layout="horizontal"
    v-bind="{
      labelCol: { span: 6 },
      wrapperCol: { span: 8 },
    }"
  >
    <a-form-model-item label="方案名称" prop="caseName">
      <a-input v-model="form.caseName" placeholder="请输入" allow-clear />
    </a-form-model-item>
    <a-form-model-item label="模型应用场景" prop="simulateType">
      <a-radio-group
        v-model="form.simulateType"
        :options="simulateTypeOptions"
        style="margin-top: 5px"
        @change="changeSimulateType"
      />
    </a-form-model-item>
    <!-- 历史调度场景：显示禁用的时间选择器 -->
    <a-form-model-item label="仿真时段" v-if="form.simulateType === 1">
      <a-range-picker
        v-model="form.range"
        :placeholder="['开始时间', '结束时间']"
        format="YYYY-MM-DD HH:00"
        :disabled-date="disabledDateNotFature"
        :show-time="{ format: 'HH:MM' }"
      />
    </a-form-model-item>

    <!-- 未来预报场景：显示选择器和预设时间 -->
    <a-form-model-item label="仿真时段" prop="range" v-if="form.simulateType === 2">
      <div style="display: flex; gap: 8px; align-items: center">
        <a-select v-model="form.forecastTimeType" style="width: 120px" @change="handleForecastTimeTypeChange">
          <a-select-option value="1">未来1天</a-select-option>
          <a-select-option value="3">未来3天</a-select-option>
          <a-select-option value="7">未来7天</a-select-option>
          <a-select-option value="custom">自定义</a-select-option>
        </a-select>

        <!-- 预设模式：显示禁用的开始时间和结束时间 -->
        <template v-if="form.forecastTimeType !== 'custom'">
          <a-date-picker
            v-model="form.presetStartTime"
            disabled
            format="YYYY-MM-DD HH:00"
            valueFormat="YYYY-MM-DD HH:00"
            :show-time="{ format: 'HH' }"
            placeholder="开始时间"
            style="flex: 1"
          />
          <span style="margin: 0 4px">至</span>
          <a-date-picker
            v-model="form.presetEndTime"
            disabled
            format="YYYY-MM-DD HH:00"
            valueFormat="YYYY-MM-DD HH:00"
            :show-time="{ format: 'HH' }"
            placeholder="结束时间"
            style="flex: 1"
          />
        </template>

        <!-- 自定义模式：显示可编辑的开始时间和结束时间 -->
        <template v-else>
          <a-date-picker
            v-model="form.customStartTime"
            format="YYYY-MM-DD HH:00"
            valueFormat="YYYY-MM-DD HH:00"
            :show-time="{ format: 'HH' }"
            :disabled-date="disabledCustomStartDate"
            placeholder="开始时间"
            style="flex: 1"
            @change="handleCustomStartTimeChange"
          />
          <span style="margin: 0 4px">至</span>
          <a-date-picker
            v-model="form.customEndTime"
            format="YYYY-MM-DD HH:00"
            valueFormat="YYYY-MM-DD HH:00"
            :show-time="{ format: 'HH' }"
            :disabled-date="disabledCustomEndDate"
            placeholder="结束时间"
            style="flex: 1"
            @change="handleCustomEndTimeChange"
          />
        </template>
      </div>
      <!-- <a-tooltip style="margin-left: 8px;">
        <template slot="title">
          <div>
            <div>• 预设模式：快速选择未来1天、3天或7天</div>
            <div>• 自定义模式：可选择未来最多10天的时间范围</div>
          </div>
        </template>
        ⓘ
      </a-tooltip> -->
    </a-form-model-item>

    <!-- <a-form-model-item label="仿真类型" prop="simulateType">
      <a-radio-group
        v-model="form.simulateType"
        :options="simulateTypeOptions"
        style="margin-top: 5px"
        @change="changeSimulateType"
      />
    </a-form-model-item>
    <a-form-model-item label="调度方案名称" prop="resvrDispId" v-if="form.simulateType === 1">
      <a-select
        v-model="form.resvrDispId"
        placeholder="请输入"
        :options="dispatchCaseOptions"
        @change="changeDispatchCase"
      />
    </a-form-model-item>
    <a-form-model-item label="仿真时段" prop="range">
      <a-range-picker
        :disabled="form.simulateType === 1"
        v-model="form.range"
        :placeholder="['开始时间', '结束时间']"
        format="YYYY-MM-DD HH:00"
        :show-time="{ format: 'HH' }"
      />
    </a-form-model-item>
    <a-form-model-item label="调度类型" prop="dispathType">
      <a-radio-group
        :disabled="form.simulateType === 1"
        v-model="form.dispathType"
        :options="dispatchTypeOptions"
        style="margin-top: 5px"
      />
    </a-form-model-item> -->
  </a-form-model>
</template>

<script>
  import moment from 'moment'
  import { getInWaterPage, getChDeptFlow, getChWaterList, getMaxOutWater } from '../../services'
  // import { dispatchTypeOptions, simulateTypeOptions } from '../../../config'
  import { dispatchTypeOptions } from '../../config'

  export default {
    name: 'BasicInfo',
    props: ['inWaterEchoData', 'fcstRangeOptions'],
    data() {
      return {
        isOutOfRange: false,
        dispatchCaseOptions: [], //调度方案
        dispatchTypeOptions,
        simulateTypeOptions: [
          { label: '历史调度', value: 1 },
          { label: '未来预报', value: 2 },
        ],
        timeType: 3,
        timeOptions: [
          { label: '未来1天', value: 1 },
          { label: '未来3天', value: 3 },
          { label: '未来7天', value: 7 },
          { label: '自定义', value: 0 },
        ],
        form: {
          caseName: undefined,
          simulateType: 2,
          range: undefined,
          inWaterId: undefined,
          dispathType: undefined,
          fcstRange: this.fcstRangeOptions[0].value,
          forecastTimeType: '3', // 默认选择未来3天
          presetStartTime: undefined, // 预设模式的开始时间
          presetEndTime: undefined, // 预设模式的结束时间
          customStartTime: undefined, // 自定义模式的开始时间
          customEndTime: undefined, // 自定义模式的结束时间
        },
        rules: {
          caseName: [{ required: true, message: '方案名称不能为空', trigger: 'blur' }],
          simulateType: [{ required: true, message: '仿真类型不能为空', trigger: 'change' }],
          range: [
            {
              validator: (rule, value, callback) => {
                if (this.form.simulateType === 1) {
                  // 历史调度场景，使用原有的range验证
                  if (!value || !value[0] || !value[1]) {
                    callback(new Error('仿真时段不能为空'))
                  } else {
                    callback()
                  }
                } else if (this.form.simulateType === 2) {
                  // 未来预报场景，验证新的时间字段
                  if (this.form.forecastTimeType === 'custom') {
                    if (!this.form.customStartTime || !this.form.customEndTime) {
                      callback(new Error('请选择完整的时间区间'))
                    } else {
                      // 校验结束时间必须晚于开始时间
                      const startTime = moment(this.form.customStartTime)
                      const endTime = moment(this.form.customEndTime)
                      if (endTime.isSameOrBefore(startTime)) {
                        callback(new Error('结束时间必须晚于开始时间'))
                      } else {
                        // 校验时间跨度不能超过10天
                        const daysDiff = endTime.diff(startTime, 'days')
                        if (daysDiff > 10) {
                          callback(new Error('时间跨度不能超过10天'))
                        } else {
                          callback()
                        }
                      }
                    }
                  } else {
                    if (!this.form.presetStartTime || !this.form.presetEndTime) {
                      callback(new Error('仿真时段不能为空'))
                    } else {
                      callback()
                    }
                  }
                } else {
                  callback()
                }
              },
              trigger: 'change',
            },
          ],
          // resvrDispId: [{ required: true, message: '调度方案不能为空', trigger: 'change' }],
          // dispathType: [{ required: true, message: '调度类型不能为空', trigger: 'change' }],
          // fcstRange: [{ required: true, message: '预报范围不能为空', trigger: 'change' }],
        },
      }
    },
    computed: {},
    watch: {
      inWaterEchoData: {
        handler(val) {
          if (!!val) {
            this.form = {
              caseName: undefined,
              simulateType: this.inWaterEchoData.scene,
              // presetStartTime: moment(this.inWaterEchoData.startTime),
              // presetEndTime: moment(this.inWaterEchoData.endTime),
              presetStartTime: this.inWaterEchoData.startTime,
              presetEndTime: this.inWaterEchoData.endTime,
              customStartTime: undefined,
              customEndTime: undefined,
              // range: [moment(this.inWaterEchoData.startTime), moment(this.inWaterEchoData.endTime)],
              resvrDispId: this.inWaterEchoData.resvrDispId,
              dispathType: this.inWaterEchoData.dispathType,
              fcstRange: this.inWaterEchoData.fcstRange,
              forecastTimeType: '3',
            }
          }
        },
      },
      'form.simulateType'(val) {
        if (val == '2') {
          // 切换到未来预报场景，初始化时间
          this.initializeForecastTime()
          this.$nextTick(() => {
            this.$refs.form.validateField('range')
          })
        } else {
          // 切换到历史调度场景，清空新增的时间字段
          this.form.forecastTimeType = '3'
          this.form.presetStartTime = undefined
          this.form.presetEndTime = undefined
          this.form.customStartTime = undefined
          this.form.customEndTime = undefined
        }
      },
    },
    created() {
      getInWaterPage({ pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
        //调度方案选项
        this.dispatchCaseOptions = (res.data?.data || []).map(el => ({
          ...el,
          label: el.caseName,
          value: el.inWaterId,
        }))
        console.log('this.dispatchCaseOptions 131', this.dispatchCaseOptions)
      })

      // 初始化未来预报场景的时间
      if (this.form.simulateType === 2) {
        this.initializeForecastTime()
      }
    },
    methods: {
      changeSimulateType(val) {
        console.log('**** 134 val', val)
        this.form.resvrDispId = undefined
        this.form.range = undefined
        this.form.dispathType = undefined
        // 清空新增的时间字段
        this.form.forecastTimeType = '3'
        this.form.presetStartTime = undefined
        this.form.presetEndTime = undefined
        this.form.customStartTime = undefined
        this.form.customEndTime = undefined
      },
      changeDispatchCase(val) {
        const obj = this.dispatchCaseOptions.find(el => el.value === val)
        this.form.range = [moment(obj.startTime), moment(obj.endTime)]
        this.form.dispathType = obj.dispathMode
      },

      // 初始化预演时间
      initializeForecastTime() {
        // 开始时间为当前时间的下一个整点
        const now = moment()
        const startTime = now.clone().add(1, 'hours').startOf('hour')

        let endTime
        const days = parseInt(this.form.forecastTimeType) || 3

        // 结束时间 = 开始时间 + 指定天数，但保持相同的小时
        endTime = startTime.clone().add(days, 'days').subtract(1, 'hours')

        this.form.presetStartTime = startTime.format('YYYY-MM-DD HH:00')
        this.form.presetEndTime = endTime.format('YYYY-MM-DD HH:00')

        // 同时更新range用于向后兼容
        this.form.range = [moment(this.form.presetStartTime), moment(this.form.presetEndTime)]
      },

      // 处理预演时间类型变化
      handleForecastTimeTypeChange(value) {
        if (value !== 'custom') {
          // 切换到预设模式，清空自定义时间并初始化预设时间
          this.form.customStartTime = undefined
          this.form.customEndTime = undefined
          this.initializeForecastTime()
        } else {
          // 切换到自定义模式，清空预设时间并初始化自定义时间
          this.form.presetStartTime = undefined
          this.form.presetEndTime = undefined
          this.initializeCustomTime()
        }
        this.$nextTick(() => {
          this.$refs.form.validateField('range')
        })
      },

      // 初始化自定义时间
      initializeCustomTime() {
        // 开始时间为当前时间的下一个整点
        const now = moment()
        const startTime = now.clone().add(1, 'hours').startOf('hour')
        const endTime = startTime.clone().add(3, 'days').subtract(1, 'hours')

        this.form.customStartTime = startTime.format('YYYY-MM-DD HH:00')
        this.form.customEndTime = endTime.format('YYYY-MM-DD HH:00')

        // 同时更新range用于向后兼容
        this.form.range = [moment(this.form.customStartTime), moment(this.form.customEndTime)]
      },

      // 处理自定义开始时间变化
      handleCustomStartTimeChange() {
        // 更新range用于向后兼容
        if (this.form.customStartTime && this.form.customEndTime) {
          this.form.range = [moment(this.form.customStartTime), moment(this.form.customEndTime)]
        }

        // 检查时间跨度是否超过10天
        if (this.form.customEndTime) {
          const startTime = moment(this.form.customStartTime)
          const endTime = moment(this.form.customEndTime)
          const daysDiff = endTime.diff(startTime, 'days')

          if (daysDiff > 10) {
            // 如果超过10天，自动调整结束时间为开始时间+10天
            const newEndTime = startTime.clone().add(10, 'days')
            this.form.customEndTime = newEndTime.format('YYYY-MM-DD HH:00')
            this.form.range = [moment(this.form.customStartTime), moment(this.form.customEndTime)]
            this.$message.warning('时间跨度不能超过10天，已自动调整结束时间')
          }
        }

        this.$nextTick(() => {
          this.$refs.form.validateField('range')
        })
      },

      // 处理自定义结束时间变化
      handleCustomEndTimeChange() {
        // 更新range用于向后兼容
        if (this.form.customStartTime && this.form.customEndTime) {
          this.form.range = [moment(this.form.customStartTime), moment(this.form.customEndTime)]
        }

        // 检查时间跨度是否超过10天
        if (this.form.customStartTime) {
          const startTime = moment(this.form.customStartTime)
          const endTime = moment(this.form.customEndTime)
          const daysDiff = endTime.diff(startTime, 'days')

          if (daysDiff > 10) {
            // 如果超过10天，自动调整开始时间为结束时间-10天
            const newStartTime = endTime.clone().subtract(10, 'days')
            this.form.customStartTime = newStartTime.format('YYYY-MM-DD HH:00')
            this.form.range = [moment(this.form.customStartTime), moment(this.form.customEndTime)]
            this.$message.warning('时间跨度不能超过10天，已自动调整开始时间')
          }
        }

        this.$nextTick(() => {
          this.$refs.form.validateField('range')
        })
      },

      // 自定义开始时间日期禁用逻辑
      disabledCustomStartDate(current) {
        // 未来预报场景：可选择今天到未来10天
        return current < moment().startOf('day') || current > moment().add(10, 'days').endOf('day')
      },

      // 自定义结束时间日期禁用逻辑
      disabledCustomEndDate(current) {
        // 未来预报场景：可选择今天到未来10天
        return current < moment().startOf('day') || current > moment().add(10, 'days').endOf('day')
      },

      save() {
        this.$refs.form.validate(valid => {
          if (valid) {
            let startTime, endTime

            if (this.form.simulateType === 2) {
              // 未来预报场景
              if (this.form.forecastTimeType === 'custom') {
                // 自定义模式：使用用户选择的开始时间和结束时间
                startTime = this.form.customStartTime
                endTime = this.form.customEndTime
              } else {
                // 预设模式：使用计算出的开始时间和结束时间
                startTime = this.form.presetStartTime
                endTime = this.form.presetEndTime
              }
            } else {
              // 历史调度场景：使用原有的range
              startTime = moment(this.form.range[0]).format('YYYY-MM-DD HH:00')
              endTime = moment(this.form.range[1]).format('YYYY-MM-DD HH:00')
            }

            this.$emit('saveData', {
              ...this.form,
              startTime,
              endTime,
            })
          } else {
            this.$emit('saveData', false)
          }
        })
      },
    },
  }
</script>

<style lang="less" scoped>
  .warning {
    color: red;
  }
</style>
